import { useEffect } from 'react';
import {
  User,
  LogOut,
  BarChart3,
  TrendingUp,
  Users,
  CheckCircle,
  Clock,
  Coins,
  RefreshCw,
  Calendar,
  Activity,
  Target
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import ConnectionStatus from './ConnectionStatus';
import { useDashboard } from '../../hooks/useDashboard';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';

export default function Dashboard() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const { data, loading, error, actions, isLoading, hasError } = useDashboard();

  useEffect(() => {
    // Load dashboard data on component mount
    actions.fetchAllData();
  }, []);

  const handleLogout = async () => {
    try {
      await apiService.logout();
    } catch (err) {
      // Logout error handled silently
    } finally {
      logout();
      navigate('/auth');
    }
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  const handleRefresh = () => {
    actions.refreshData();
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getArchetypeColor = (archetype) => {
    const colors = {
      'The Analytical Innovator': 'bg-blue-100 text-blue-800',
      'The Creative Collaborator': 'bg-purple-100 text-purple-800',
      'The Strategic Leader': 'bg-green-100 text-green-800',
      'The Empathetic Helper': 'bg-pink-100 text-pink-800'
    };
    return colors[archetype] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-indigo-100 rounded-xl">
                <BarChart3 className="h-8 w-8 text-indigo-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p className="text-sm text-gray-600 flex items-center space-x-1">
                  <span>Welcome back,</span>
                  <span className="font-medium text-indigo-600">{user?.email || 'User'}</span>
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <ConnectionStatus />

              {/* Refresh Button */}
              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
                title="Refresh Dashboard"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">Refresh</span>
              </button>

              {/* Debug Assessment Button - Only show in development */}
              {import.meta.env.DEV && (
                <button
                  onClick={() => navigate('/assessment?debug=true')}
                  className="flex items-center space-x-2 px-4 py-2 text-white bg-orange-500 rounded-lg hover:bg-orange-600 transition-colors"
                  title="Debug Assessment (Auto-filled)"
                >
                  <span className="text-sm">🔧</span>
                  <span className="hidden sm:inline text-sm">Debug Assessment</span>
                </button>
              )}

              <button
                onClick={handleProfile}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">Profile</span>
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <LogOut className="h-4 w-4" />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {loading.initial && (
          <div className="flex justify-center items-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        )}

        {/* Error State */}
        {hasError && !loading.initial && (
          <ErrorMessage
            title="Failed to Load Dashboard"
            message={error.general || error.stats || error.results || error.tokenBalance}
            onRetry={handleRefresh}
            retryText="Retry"
          />
        )}

        {/* Dashboard Content */}
        {!loading.initial && !hasError && (
          <div className="space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {/* Token Balance Card */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Coins className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Token Balance</dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {loading.tokenBalance ? (
                            <div className="animate-pulse h-6 bg-gray-200 rounded w-16"></div>
                          ) : (
                            data.tokenBalance?.tokenBalance || 0
                          )}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-5 py-3">
                  <div className="text-sm">
                    <span className="text-gray-600">
                      Last updated: {data.tokenBalance?.lastUpdated ? formatDate(data.tokenBalance.lastUpdated) : 'N/A'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Total Results Card */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Target className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Results</dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {loading.stats ? (
                            <div className="animate-pulse h-6 bg-gray-200 rounded w-16"></div>
                          ) : (
                            data.stats?.total_results || 0
                          )}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Total Jobs Card */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Activity className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Total Jobs</dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {loading.stats ? (
                            <div className="animate-pulse h-6 bg-gray-200 rounded w-16"></div>
                          ) : (
                            data.stats?.total_jobs || 0
                          )}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>

              {/* Completed Assessments Card */}
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="p-5">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <dl>
                        <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                        <dd className="text-lg font-medium text-gray-900">
                          {loading.stats ? (
                            <div className="animate-pulse h-6 bg-gray-200 rounded w-16"></div>
                          ) : (
                            data.stats?.completed_assessments || 0
                          )}
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Archetype Distribution and Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Archetype Distribution */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <Users className="h-5 w-5 text-indigo-600 mr-2" />
                    Archetype Distribution
                  </h3>
                </div>
                <div className="p-6">
                  {loading.stats ? (
                    <div className="space-y-3">
                      {[1, 2, 3, 4].map((i) => (
                        <div key={i} className="animate-pulse flex items-center justify-between">
                          <div className="h-4 bg-gray-200 rounded w-32"></div>
                          <div className="h-4 bg-gray-200 rounded w-8"></div>
                        </div>
                      ))}
                    </div>
                  ) : data.stats?.archetype_distribution ? (
                    <div className="space-y-4">
                      {Object.entries(data.stats.archetype_distribution).map(([archetype, count]) => (
                        <div key={archetype} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getArchetypeColor(archetype)}`}>
                              {archetype}
                            </span>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{count}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <Users className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No archetype data</h3>
                      <p className="mt-1 text-sm text-gray-500">Complete some assessments to see archetype distribution.</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <Clock className="h-5 w-5 text-indigo-600 mr-2" />
                    Recent Activity
                  </h3>
                </div>
                <div className="p-6">
                  {loading.stats ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="animate-pulse">
                          <div className="flex items-center space-x-3">
                            <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : data.stats?.recent_activity?.length > 0 ? (
                    <div className="space-y-4">
                      {data.stats.recent_activity.map((activity) => (
                        <div key={activity.id} className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                              activity.status === 'completed' ? 'bg-green-100' : 'bg-yellow-100'
                            }`}>
                              {activity.status === 'completed' ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                              ) : (
                                <Clock className="h-4 w-4 text-yellow-600" />
                              )}
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {activity.archetype}
                            </p>
                            <p className="text-sm text-gray-500">
                              {formatDate(activity.created_at)} • {activity.status}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No recent activity</h3>
                      <p className="mt-1 text-sm text-gray-500">Your recent assessments will appear here.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Recent Results */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center">
                    <TrendingUp className="h-5 w-5 text-indigo-600 mr-2" />
                    Recent Results
                  </h3>
                  <button
                    onClick={() => navigate('/results')}
                    className="text-sm text-indigo-600 hover:text-indigo-500"
                  >
                    View all
                  </button>
                </div>
              </div>
              <div className="p-6">
                {loading.results ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="flex items-center space-x-4">
                          <div className="h-10 w-10 bg-gray-200 rounded-lg"></div>
                          <div className="flex-1 space-y-2">
                            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                          </div>
                          <div className="h-6 w-16 bg-gray-200 rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : data.results?.length > 0 ? (
                  <div className="space-y-4">
                    {data.results.slice(0, 5).map((result) => (
                      <div key={result.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex-shrink-0">
                          <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                            result.status === 'completed' ? 'bg-green-100' :
                            result.status === 'processing' ? 'bg-yellow-100' : 'bg-red-100'
                          }`}>
                            {result.status === 'completed' ? (
                              <CheckCircle className="h-5 w-5 text-green-600" />
                            ) : result.status === 'processing' ? (
                              <Clock className="h-5 w-5 text-yellow-600" />
                            ) : (
                              <Activity className="h-5 w-5 text-red-600" />
                            )}
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {result.assessment_name || 'AI-Driven Talent Mapping'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatDate(result.created_at)}
                          </p>
                        </div>
                        <div className="flex-shrink-0">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            result.status === 'completed' ? 'bg-green-100 text-green-800' :
                            result.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {result.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <Activity className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No results yet</h3>
                    <p className="mt-1 text-sm text-gray-500">Take an assessment to see your results here.</p>
                    <div className="mt-6">
                      <button
                        onClick={() => navigate('/assessment')}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        Start Assessment
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
